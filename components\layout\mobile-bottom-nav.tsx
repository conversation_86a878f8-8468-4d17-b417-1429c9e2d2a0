"use client";

import React, { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { 
  Palette,
  Image, 
  Users, 
  User
} from "lucide-react";
import { useUser } from "@/lib/contexts/UserContext";

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

const navItems: NavItem[] = [
  {
    path: "/studio",
    label: "Studio",
    icon: Palette,
    gradient: "from-violet-500 to-purple-500"
  },
  {
    path: "/gallery",
    label: "Gallery",
    icon: Image,
    gradient: "from-violet-500 to-blue-500"
  },
  {
    path: "/community",
    label: "Community",
    icon: Users,
    gradient: "from-emerald-500 to-teal-500"
  },
  {
    path: "/profile",
    label: "Profile",
    icon: User,
    gradient: "from-violet-500 to-blue-500"
  }
];

// NOTE: MobileBottomNav uses useUser and should only be used in protected layouts/routes.
// If you need a public mobile nav, create a separate PublicMobileBottomNav component without useUser.
export function MobileBottomNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, isLoading } = useUser();
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Hide navigation if user is not logged in or component has not mounted
  if (!hasMounted || isLoading || !user) return null;



  return (
    <nav
      role="navigation"
      aria-label="Main mobile navigation"
      className="fixed bottom-0 left-0 right-0 z-[9999] mobile-bottom-nav"
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        width: '100vw',
        zIndex: 9999,
        WebkitTransform: 'translateZ(0)',
        transform: 'translateZ(0)',
        willChange: 'transform'
      }}
    >
      <div
        className="glass backdrop-blur-xl w-full overflow-hidden"
        style={{
          background: 'rgba(15, 23, 42, 0.98)',
          backdropFilter: 'blur(24px)',
          WebkitBackdropFilter: 'blur(24px)',
          boxShadow: '0 -4px 32px rgba(0, 0, 0, 0.5), 0 -1px 0 rgba(255, 255, 255, 0.1)',
          minHeight: 'calc(80px + env(safe-area-inset-bottom, 0px))',
          paddingBottom: 'env(safe-area-inset-bottom, 0px)',
          width: '100vw'
        }}
      >
        <div className="flex items-center justify-between py-3 px-2 w-full">
          {navItems.map(({ icon: Icon, label, path, gradient }) => {
            const isActive = pathname === path;
            
            return (
              <button
                key={path}
                onClick={() => router.push(path)}
                aria-current={isActive ? "page" : undefined}
                className={cn(
                  "flex flex-col items-center space-y-1.5 px-1 py-2 transition-all duration-300 ease-out nav-transition group touch-manipulation flex-1 min-w-0",
                  "focus:outline-none focus:ring-2 focus:ring-slate-400/20 focus:ring-offset-2 focus:ring-offset-slate-900",
                  "border-none bg-transparent transform-gpu",
                  "active:scale-95 hover:scale-105 md:hover:scale-100",
                  isActive
                    ? "text-white"
                    : "text-slate-400 hover:text-white active:text-white"
                )}
                style={{
                  border: 'none',
                  background: 'transparent',
                  outline: 'none',
                  boxShadow: 'none'
                }}
              >
                <div
                  className={cn(
                    "relative p-2.5 rounded-xl transition-all duration-300 ease-out flex-shrink-0 transform-gpu",
                    "group-active:scale-90 group-hover:scale-110 md:group-hover:scale-100",
                    isActive
                      ? `bg-gradient-to-r ${gradient} shadow-lg shadow-purple-500/25`
                      : "bg-slate-700/80 border border-slate-600/50 group-hover:bg-slate-600/90 group-hover:border-slate-500/60"
                  )}
                  style={{
                    border: 'none'
                  }}
                >
                  {isActive && (
                    <div
                      className={cn(
                        "absolute inset-0 bg-gradient-to-r rounded-xl blur-lg opacity-60 animate-pulse",
                        gradient
                      )}
                    ></div>
                  )}
                  <Icon
                    className={cn(
                      "h-5 w-5 relative z-10 transition-all duration-300 ease-out text-white transform-gpu",
                      isActive ? "scale-110" : "group-hover:scale-105 md:group-hover:scale-100"
                    )}
                  />
                </div>
                <span
                  className={cn(
                    "text-xs font-medium transition-all duration-300 ease-out text-center leading-tight truncate w-full transform-gpu",
                    isActive
                      ? "text-white font-semibold scale-105"
                      : "text-slate-400 group-hover:text-slate-300 group-active:text-white group-hover:scale-105 md:group-hover:scale-100"
                  )}
                >
                  {label}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    </nav>
  );
} 
